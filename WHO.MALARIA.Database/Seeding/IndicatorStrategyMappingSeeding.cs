using System;
using System.Linq;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.SeedingMetadata;

namespace WHO.MALARIA.Database.Seeding
{
    /// <summary>
    /// Seeding data of IndicatorStrategyMapping.
    /// </summary>
    public class IndicatorStrategyMappingSeeding
    {
        /// <summary>
        /// Save indicator and strategy mappings in IndicatorStrategyMapping table
        /// </summary>
        /// <param name="malariaDbContext">Instance of MalariaDbContext</param>
        public static void Seed(MalariaDbContext malariaDbContext)
        {
            DateTime currentDate = DateTime.UtcNow;

            IQueryable<IndicatorStrategyMapping> existingRecords = malariaDbContext.IndicatorStrategyMappings.AsQueryable();

            IndicatorStrategyMapping[] newRecords = new IndicatorStrategyMapping[]
                {
                        // indicator 1.1.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_1,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now,
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_147,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now,
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_401,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now,
                        },

                        // indicator 1.1.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_2,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_2,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_3,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_402,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.1.3
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_4,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_3,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_5,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_3,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_403,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_3,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.1.4
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_6,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_4,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_7,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_4,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_404,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_4,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                         // indicator 1.1.5
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_8,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_5,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_405,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_5,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.1.6
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_9,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_6,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_406,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_6,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.1.7
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_10,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_7,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_407,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_7,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.1.8
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_12,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_8,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_13,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_8,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_14,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_8,
                            StrategyId = StrategySeedingMetadata.MDA_ID,
                            ParentCaseStrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_155,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_8,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_408,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_8,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.1.9
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_16,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_9,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_17,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_9,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_18,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_9,
                            StrategyId = StrategySeedingMetadata.MDA_ID,
                            ParentCaseStrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_19,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_9,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_20,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_9,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_409,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_1_9,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                         // indicator 1.2.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_21,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_22,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_458,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                         // indicator 1.2.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_23,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_459,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        // indicator 1.2.3
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_24,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_3,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_25,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_3,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_460,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_3,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.2.4
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_26,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_4,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_461,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_4,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.2.5
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_27,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_5,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_462,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_5,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                         // indicator 1.2.6
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_28,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_6,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_463,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_6,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.2.7
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_29,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_7,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_400,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_7,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_464,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_7,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },                    

                         // indicator 1.2.8
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_30,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_8,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_156,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_8,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_465,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_8,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.2.9
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_31,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_9,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_32,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_9,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_466,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_9,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.2.10
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_33,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_10,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_34,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_10,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_467,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_10,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.2.11
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_35,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_11,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_36,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_11,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_468,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_11,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.2.12
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_37,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_12,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_38,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_12,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_469,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_12,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.2.13
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_39,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_13,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_470,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_13,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.2.14
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_40,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_14,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_41,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_14,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_471,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_2_14,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                         // indicator 1.3.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_42,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_43,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_410,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_163,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_1,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_164,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_1,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_165,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_1,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_166,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_1,
                            StrategyId = StrategySeedingMetadata.MDA_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_167,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_1,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_168,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_1,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_169,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_1,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_170,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_1,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_171,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_1,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_172,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_1,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_173,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_1,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                         new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_174,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_1,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.3.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_44,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_2,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_45,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_411,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_175,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_2,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_176,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_2,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_177,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_2,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_178,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_2,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_179,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_2,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_180,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_2,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_181,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_2,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.3.3
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_46,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_3,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_47,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_3,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_472,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_3,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_182,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_3,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_183,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_3,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_184,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_3,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_185,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_3,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_186,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_3,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_187,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_3,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_188,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_3,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_189,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_3,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_190,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_3,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_191,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_3,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_192,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_3,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.3.4
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_48,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_4,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_49,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_4,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_412,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_4,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_193,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_4,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_194,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_4,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_195,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_4,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_196,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_4,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_197,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_4,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_198,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_4,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_199,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_4,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_200,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_4,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_201,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_4,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_202,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_4,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                       new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_203,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_4,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.3.5
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_50,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_5,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_413,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_5,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                         // indicator 1.3.6
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_51,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_6,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_414,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_6,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 1.3.7
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_52,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_7,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_53,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_7,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_415,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_7,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_204,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_7,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_205,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_7,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_206,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_7,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_207,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_7,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_208,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_7,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_209,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_7,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_210,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_7,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_211,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_7,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_212,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_7,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_213,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_7,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_214,
                            IndicatorId = IndicatorSeedingMetadata.IND_1_3_7,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 2.1.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_54,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_55,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_416,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 2.1.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_56,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_2,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_57,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_417,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 2.1.3
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_157,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_3,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_58,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_3,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_418,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_3,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 2.1.4
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_59,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_4,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_60,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_4,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_419,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_4,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_215,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_4,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_216,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_4,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_217,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_4,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_218,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_4,
                            StrategyId = StrategySeedingMetadata.MDA_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_219,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_4,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_220,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_4,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_221,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_4,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_222,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_4,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_223,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_4,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_224,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_4,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_225,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_1_4,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 2.2.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_61,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_62,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_420,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_226,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_1,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_227,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_1,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_228,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_1,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_229,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_1,
                            StrategyId = StrategySeedingMetadata.MDA_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_230,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_1,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_231,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_1,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_232,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_1,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_233,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_1,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_234,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_1,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_235,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_1,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_236,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_1,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_237,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_1,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 2.2.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_63,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_2,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_64,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_421,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_238,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_2,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_239,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_2,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_240,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_2,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_241,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_2,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_242,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_2,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_243,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_2,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_244,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_2,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_245,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_2,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_246,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_2,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_247,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_2,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                          new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_248,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_2,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 2.2.3
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_65,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_3,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_422,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_3,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 2.2.4
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_66,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_4,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_67,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_4,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_423,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_4,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 2.2.5
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_68,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_5,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_69,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_5,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_424,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_5,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 2.2.6
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_70,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_6,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_71,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_6,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_425,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_2_6,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 2.3.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_72,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_73,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_426,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_249,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_1,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_250,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_1,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_251,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_1,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_252,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_1,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_253,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_1,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_254,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_1,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_255,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_1,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_256,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_1,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_257,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_1,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_258,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_1,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_259,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_1,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 2.3.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_74,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_2,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_75,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_427,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_3_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 2.4.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_78,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_79,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_428,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_260,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_1,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_261,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_1,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_262,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_1,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_263,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_1,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_264,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_1,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_265,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_1,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_266,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_1,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_267,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_1,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_268,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_1,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_269,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_1,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                         new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_270,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_1,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 2.4.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_80,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_2,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_81,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_429,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_271,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_2,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_272,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_2,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_273,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_2,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_274,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_2,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_275,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_2,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_276,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_2,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_277,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_2,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_278,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_2,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_279,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_2,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_280,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_2,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_281,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_2,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 2.4.3
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_82,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_3,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_83,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_3,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_473,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_3,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                         // indicator 2.4.4
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_84,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_4,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_85,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_4,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_430,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_4_4,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                         // indicator 2.5.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_86,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_5_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_87,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_5_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_431,
                            IndicatorId = IndicatorSeedingMetadata.IND_2_5_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.1.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_92,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_1_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_93,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_1_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_474,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_1_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.1.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_94,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_1_2,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_95,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_1_2,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_432,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_1_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = StrategySeedingMetadata.CT_ID,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.1.3
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_96,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_1_3,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_97,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_1_3,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_434,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_1_3,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = StrategySeedingMetadata.CT_ID,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.2.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_98,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_99,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_436,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_282,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_1,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_283,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_1,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_284,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_1,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_285,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_1,
                            StrategyId = StrategySeedingMetadata.MDA_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_286,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_1,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_287,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_1,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_288,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_1,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_289,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_1,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_290,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_1,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_291,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_1,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_292,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_1,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_293,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_1,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.2.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_100,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_2,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_101,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_437,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_294,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_2,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                         new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_295,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_2,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_296,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_2,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_297,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_2,
                            StrategyId = StrategySeedingMetadata.MDA_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_298,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_2,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_299,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_2,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_300,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_2,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_301,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_2,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_302,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_2,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_303,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_2,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_304,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_2,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                         new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_305,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_2,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.2.3
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_102,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_3,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_103,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_3,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_438,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_3,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_306,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_3,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_307,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_3,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_308,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_3,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_309,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_3,
                            StrategyId = StrategySeedingMetadata.MDA_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_310,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_3,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_311,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_3,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_312,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_3,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_313,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_3,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_314,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_3,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_315,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_3,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_316,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_3,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_317,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_3,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.2.4
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_104,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_4,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_154,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_4,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_475,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_2_4,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.3.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_105,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_106,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_439,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.3.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_107,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_2,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_108,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_440,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_318,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_2,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_319,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_2,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_320,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_2,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_321,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_2,
                            StrategyId = StrategySeedingMetadata.MDA_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_322,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_2,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_323,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_2,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_324,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_2,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_325,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_2,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_326,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_2,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_327,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_2,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_328,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_2,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_329,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_2,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                         // indicator 3.3.3
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_109,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_3,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_110,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_3,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_441,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_3,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.3.4
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_111,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_4,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_162,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_4,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_442,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_4,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.3.5
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_112,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_5,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_153,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_5,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_476,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_3_5,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.4.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_113,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_114,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_443,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.4.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_115,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_2,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_116,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_444,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_330,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_2,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_331,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_2,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_332,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_2,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_333,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_2,
                            StrategyId = StrategySeedingMetadata.MDA_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_334,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_2,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                         new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_335,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_2,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_336,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_2,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_337,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_2,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_338,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_2,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_339,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_2,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_340,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_2,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.4.3
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_117,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_3,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_477,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_4_3,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.5.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_118,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_119,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_445,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.5.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_120,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_2,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_121,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_446,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_341,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_2,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_342,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_2,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_343,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_2,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_344,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_2,
                            StrategyId = StrategySeedingMetadata.MDA_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_345,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_2,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_346,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_2,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_347,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_2,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_348,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_2,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_349,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_2,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_350,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_2,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_351,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_2,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_352,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_2,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.5.3
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_122,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_3,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_123,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_3,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_447,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_3,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                         // indicator 3.5.4
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_124,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_4,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_478,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_5_4,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.6.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_125,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_126,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_448,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_353,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_1,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_354,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_1,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_355,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_1,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_356,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_1,
                            StrategyId = StrategySeedingMetadata.MDA_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_357,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_1,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_358,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_1,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_359,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_1,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_360,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_1,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_361,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_1,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_362,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_1,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_363,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_1,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_364,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_1,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.6.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_127,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_2,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_128,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_479,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 3.6.3
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_129,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_3,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_152,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_3,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_480,
                            IndicatorId = IndicatorSeedingMetadata.IND_3_6_3,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 4.1.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_130,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_131,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_449,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_365,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_1,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_366,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_1,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_367,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_1,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_368,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_1,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_369,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_1,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_370,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_1,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_371,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_1,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_372,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_1,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_373,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_1,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_374,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_1,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_375,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_1,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                         // indicator 4.1.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_132,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_2,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_158,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_450,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                         // indicator 4.1.3
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_133,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_3,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_159,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_3,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_451,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_1_3,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                         // indicator 4.2.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_134,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_2_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_150,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_2_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_452,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_2_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                         // indicator 4.2.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_135,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_2_2,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_151,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_2_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_453,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_2_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 4.3.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_136,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_137,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_454,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_376,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_1,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_377,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_1,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_378,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_1,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_379,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_1,
                            StrategyId = StrategySeedingMetadata.MDA_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_380,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_1,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_381,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_1,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_382,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_1,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_383,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_1,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_384,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_1,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_385,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_1,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_386,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_1,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_387,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_1,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                         // indicator 4.3.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_138,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_2,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_139,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_481,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                         // indicator 4.3.3
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_140,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_3,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_482,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_3_3,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 4.4.1
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_141,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_1,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_142,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_1,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_455,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_1,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_388,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_1,
                            StrategyId = StrategySeedingMetadata.IPTP_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_389,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_1,
                            StrategyId = StrategySeedingMetadata.IPTI_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_390,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_1,
                            StrategyId = StrategySeedingMetadata.SMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_391,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_1,
                            StrategyId = StrategySeedingMetadata.MDA_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_392,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_1,
                            StrategyId = StrategySeedingMetadata.ITNSRC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_393,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_1,
                            StrategyId = StrategySeedingMetadata.ITNSMC_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_394,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_1,
                            StrategyId = StrategySeedingMetadata.VC_IRS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_395,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_1,
                            StrategyId = StrategySeedingMetadata.VC_LSM_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_396,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_1,
                            StrategyId = StrategySeedingMetadata.CT_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_397,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_1,
                            StrategyId = StrategySeedingMetadata.ES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_398,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_1,
                            StrategyId = StrategySeedingMetadata.DES_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_399,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_1,
                            StrategyId = StrategySeedingMetadata.GS_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Priority,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 4.4.2
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_143,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_2,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_160,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_2,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_456,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_2,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 4.4.3
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_144,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_3,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_148,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_3,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_457,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_3,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },

                        // indicator 4.4.4
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_145,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_4,
                            StrategyId = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_149,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_4,
                            StrategyId = StrategySeedingMetadata.ELIMINATION_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        },
                        new IndicatorStrategyMapping
                        {
                            Id = IndicatorStrategyMappingSeedingMetadata.Mapping_ID_483,
                            IndicatorId = IndicatorSeedingMetadata.IND_4_4_4,
                            StrategyId = StrategySeedingMetadata.Both_ID,
                            ParentCaseStrategyId = null,
                            IndicatorPriority = (int)Priority.Optional,
                            CreatedBy =  null,
                            CreatedAt = DateTime.Now
                        }
                     };

            // 1. delete the existing record in the database if they are not in the new records
            foreach (IndicatorStrategyMapping existingRecord in existingRecords)
            {
                if (!newRecords.Any(nr => nr.Id == existingRecord.Id))
                {
                    malariaDbContext.IndicatorStrategyMappings.Remove(existingRecord);
                }
            }

            foreach (IndicatorStrategyMapping newRecord in newRecords)
            {
                IndicatorStrategyMapping existingRecord = existingRecords.SingleOrDefault(existing => existing.Id == newRecord.Id);
                if (existingRecord != null)
                {
                    // 2.
                    // if the record already exists in the database and 
                    // is modified then update it in the database
                    if (IsRecordUpdated(existingRecord, newRecord))
                    {
                        existingRecord.IndicatorId = newRecord.IndicatorId;
                        existingRecord.IndicatorPriority = newRecord.IndicatorPriority;
                        existingRecord.StrategyId = newRecord.StrategyId;
                        existingRecord.ParentCaseStrategyId = newRecord.ParentCaseStrategyId;
                        existingRecord.IndicatorPriority = newRecord.IndicatorPriority;
                        existingRecord.UpdatedBy = null;
                        existingRecord.UpdatedAt = currentDate;

                        malariaDbContext.IndicatorStrategyMappings.Update(existingRecord);
                    }
                }
                else
                {
                    // 3. else add the new record into the database
                    malariaDbContext.IndicatorStrategyMappings.Add(newRecord);
                }
            }
            malariaDbContext.SaveChanges();
        }

        private static bool IsRecordUpdated(IndicatorStrategyMapping existingRecord, IndicatorStrategyMapping newRecord)
        {
            return (existingRecord.IndicatorId != newRecord.IndicatorId
                    || existingRecord.StrategyId != newRecord.StrategyId
                    || existingRecord.IndicatorPriority != newRecord.IndicatorPriority
                    || existingRecord.ParentCaseStrategyId != newRecord.ParentCaseStrategyId
                    || existingRecord.IndicatorPriority != newRecord.IndicatorPriority);
        }
    }
}